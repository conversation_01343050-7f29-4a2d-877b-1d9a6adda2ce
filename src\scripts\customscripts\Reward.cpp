#pragma execution_character_set("utf-8")
#include "Reward.h"
//#include "OtherFun.h"
#include "Requirement.h"
#include "custom_itemScript.h"
//#include "vip.h"

std::unordered_map<uint32, RewTemplate> RewMap;

void Reward::Load()
{
    RewMap.clear();
    auto result = WorldDatabase.PQuery("SELECT 奖励模板ID,金币数量,积分数量,经验数量,斗气点数,荣誉点数,竞技点数,"
                                                               "物品ID1,物品数量1,物品ID2,物品数量2,物品ID3,物品数量3,物品ID4,物品数量4,物品ID5,物品数量5,"
                                                               "物品ID6,物品数量6,物品ID7,物品数量7,物品ID8,物品数量8,物品ID9,物品数量9,物品ID10,物品数量10,GM命令组 FROM _模板_奖励");
    if (!result)
        return;
    do
    {
        Field* fields = result->Fetch();
        uint32 rewId = fields[0].GetUInt32();
        RewTemplate RewTemp;
        RewTemp.goldCount = fields[1].GetUInt32() * GOLD;
        RewTemp.tokenCount = fields[2].GetUInt32();
        RewTemp.xp = fields[3].GetUInt32();
        RewTemp.DouQiPoint = fields[4].GetUInt32();
        RewTemp.hrPoints = fields[5].GetUInt32();
        RewTemp.arenaPoints = fields[6].GetUInt32();

        for (size_t i = 0; i < REW_ITEM_MAX; i++)
        {
            uint32 entry = fields[7 + 2 * i].GetUInt32();
            uint32 count = fields[8 + 2 * i].GetUInt32();

            ItemPrototype const* pProto = sObjectMgr.GetItemPrototype(entry);

            if (entry && count > 0)
            {
                RewItemTemplate temp;
                temp.itemId = entry;
                temp.itemCount = count;
                RewTemp.ItemDataVec.push_back(temp);
            }
        }

        Tokenizer commandData(fields[27].GetString(), '#');
        for (Tokenizer::const_iterator itr = commandData.begin(); itr != commandData.end(); ++itr)
        {
            Tokenizer commands(*itr, '$');
            RewCommandTemplate temp;
            if (commands.size() > 0)
                temp.command = commands[0];
            else
                temp.command = "";

            if (commands.size() > 1)
                temp.icon = commands[1];
            else
                temp.icon = "";

            if (commands.size() > 2)
                temp.des = commands[2];
            else
                temp.des = "";

            RewTemp.CommandDataVec.push_back(temp);
        }
        RewMap.insert(std::make_pair(rewId, RewTemp));
    }
    while (result->NextRow());

    delete result;
    sLog.outString(">>加载 _模板_奖励 成功.");
}

void Reward::Rew(Player* player, uint32 rewId, uint32 muilt)
{
    if (rewId == 0)
        return;

    std::unordered_map<uint32, RewTemplate>::iterator iter = RewMap.find(rewId);
    if (iter != RewMap.end())
    {
        uint32 xp = muilt * iter->second.xp;
        uint32 goldCount = muilt * iter->second.goldCount;
        uint32 tokenCount = muilt * iter->second.tokenCount;
        uint32 hrPoints = muilt * iter->second.hrPoints;
        uint32 arenaPoints = muilt * iter->second.arenaPoints;
        uint32 DouQiPoint = muilt * iter->second.DouQiPoint;

        if (xp > 0)
        {
            player->GiveXP(xp, player);
            std::ostringstream oss;
            oss << "获得"
                << "|cFFFFCC00[经验]|r X " << xp;
            player->GetSession()->SendAreaTriggerMessage(oss.str().c_str());
        }

        if (goldCount > 0)
        {
            player->ModifyMoney(goldCount);
            std::ostringstream oss;
            oss << "获得"
                << "|cFFFFCC00[金币]|r"
                << " X " << goldCount / GOLD;
            player->GetSession()->SendAreaTriggerMessage(oss.str().c_str());
        }

        if (tokenCount > 0)
        {
            // 获得积分
            AddJiFenByPlayer(player, tokenCount);
        }

        if (hrPoints > 0)
        {
            // William TODO
            // player->ModifyHonorPoints(hrPoints);
            // std::ostringstream oss;
            // oss << "获得" << "|cFFFFCC00[荣誉]|r" << " X " << hrPoints;
            // player->GetSession()->SendAreaTriggerMessage(oss.str().c_str());
        }

        if (arenaPoints > 0)
        {
            // William TODO
            // player->ModifyArenaPoints(arenaPoints);
            // std::ostringstream oss;
            // oss << "获得" << "|cFFFFCC00[竞技点]|r" << " X " << arenaPoints;
            // player->GetSession()->SendAreaTriggerMessage(oss.str().c_str());
        }

        if (DouQiPoint > 0) // 巅峰斗气
        {
            //sCharMod->UpdateRankDate(player, DouQiPoint, true);
        }

        for (auto itr = iter->second.ItemDataVec.begin(); itr != iter->second.ItemDataVec.end(); itr++)
            // player->AddItem(itr->itemId, itr->itemCount);
            RewItem(player, itr->itemId, itr->itemCount);

        for (auto itr = iter->second.CommandDataVec.begin(); itr != iter->second.CommandDataVec.end(); itr++)
            if (!itr->command.empty())
                sCustomCommand->DoCommand(player, itr->command);
    }
    else
        sWorld.SendWorldText(3, "数据表[_模板_奖励]未正常配置");
}

void Reward::RewItem(Player* player, uint32 itemId, uint32 itemCount)
{
    uint32 count = itemCount;
    uint32 noSpaceForCount = 0;
    ItemPosCountVec dest;
    InventoryResult msg = player->CanStoreNewItem(NULL_BAG, NULL_SLOT, dest, itemId, count, &noSpaceForCount);
    if (msg != EQUIP_ERR_OK)
        count -= noSpaceForCount;

    if (count == 0 || dest.empty())
    {
    }
    else
    {
        Item* item = player->StoreNewItem(dest, itemId, true, Item::GenerateItemRandomPropertyId(itemId));
        if (item)
            player->SendNewItem(item, count, true, false);
    }

    // bool add = player->AddItem(itemId, itemCount);

    itemCount -= count;

    if (itemCount > 0)
    {
        // if (ItemPrototype const* pProto = sObjectMgr.GetItemPrototype(itemId))
        //{

        //	MailDraft draft;
        //	MailSender sender(MAIL_NORMAL, player->GetObjectGuid().GetCounter(), MAIL_STATIONERY_GM);
        //	draft.SendMailTo(MailReceiver(player, player->GetObjectGuid().GetCounter()), sender);
        //	ChatHandler(player->GetSession()).PSendSysMessage("你收到新邮件:%s X %u", sItemMod->GetItemLink(itemId).c_str(), itemCount);
        //}

        ItemPrototype const* markProto = sObjectMgr.GetItemPrototype(itemId);
        if (!markProto)
            return;

        if (Item* markItem = Item::CreateItem(itemId, itemCount, player))
        {
            // save new item before send
            markItem->SaveToDB(); // save for prevent lost at next mail load, if send fail then item will deleted

            // subject: item name
            std::string subject = markProto->Name1;
            int loc_idx = player->GetSession()->GetSessionDbLocaleIndex();
            if (loc_idx >= 0)
                if (ItemLocale const* il = sObjectMgr.GetItemLocale(markProto->ItemId))
                    if (il->Name.size() > size_t(loc_idx) && !il->Name[loc_idx].empty())
                        subject = il->Name[loc_idx];

            // text
            std::string textFormat = "在您最近一次参与活动中， %s 我们试着奖励您，但是失败了. 您会在您的信箱中收到我们这次无法发到背包内的奖励. 感谢您的支持 %s!";
            char textBuf[300];
            snprintf(textBuf, 300, textFormat.c_str(), player->GetName(), player->GetName());

            MailDraft(subject, textBuf).AddItem(markItem).SendMailTo(player, MailSender(MAIL_NORMAL, player->GetSession() ? player->GetSession()->GetPlayer()->GetObjectGuid().GetCounter() : 0, MAIL_STATIONERY_GM), MAIL_CHECK_MASK_COPIED, 1, 1 * DAY);
        }
    }
}

bool Reward::IsExist(uint32 rewId)
{
    std::unordered_map<uint32, RewTemplate>::iterator iter = RewMap.find(rewId);
    if (iter != RewMap.end())
        return true;
    return false;
}
// 物品增加描述
std::string Reward::GetSellDes(uint32 entry)
{
    uint32 rewId = 0;
    uint32 chance = 0;
    std::string command = "";

    sItemMod->GetSaleInfo(entry, rewId, chance, command);

    if (rewId == 0)
        return "";

    std::ostringstream oss;
    oss << "|cFF00FF00『出售 |cFFFFCC00";
    oss << chance;
    oss << "%|r |cFF00FF00的几率获得』|r\n";
    oss << sRew->GetDescription(rewId, false);
    return oss.str();
}

std::string Reward::GetDescription(uint32 rewId, bool quest)
{
    std::unordered_map<uint32, RewTemplate>::iterator iter = RewMap.find(rewId);
    if (iter != RewMap.end())
    {
        uint32 xp = iter->second.xp;
        uint32 goldCount = iter->second.goldCount;
        uint32 tokenCount = iter->second.tokenCount;
        uint32 hrPoints = iter->second.hrPoints;
        uint32 arenaPoints = iter->second.arenaPoints;

        std::ostringstream oss;

        if (xp > 0)
            oss << "|cFFFFCC00『经验』|r x |cff00ff00" << xp << "|r\n";

        if (goldCount > 0)
            oss << "|cFFFFCC00『金币』|r x |cff00ff00" << goldCount / GOLD << "|r\n";

        if (tokenCount > 0)
            oss << "|cFFFFCC00『点券』|r x |cff00ff00" << tokenCount << "|r\n";

        if (hrPoints > 0)
            oss << "|cFFFFCC00『荣誉』|r x |cff00ff00" << hrPoints << "|r\n";

        if (arenaPoints > 0)
            oss << "|cFFFFCC00『竞技点』|r x |cff00ff00" << arenaPoints << "|r\n";

        for (auto itr = iter->second.ItemDataVec.begin(); itr != iter->second.ItemDataVec.end(); itr++)
            if (ItemPrototype const* pProto = sObjectMgr.GetItemPrototype(itr->itemId))
                oss << sItemMod->GetItemLink(itr->itemId) << " x |cff00ff00" << itr->itemCount << "|r\n";

        for (auto itr = iter->second.SpellDataVec.begin(); itr != iter->second.SpellDataVec.end(); itr++)
        {
            if (*itr < 0)
                continue;

            uint32 spellId = *itr;

            auto spellInfo = sSpellMgr.GetSpellEntry(spellId);
            if (spellInfo)
                oss << "|cFFFFCC00『技能』『" << spellInfo->SpellName[4] << "』|r\n";
        }

        for (auto itr = iter->second.SpellDataVec.begin(); itr != iter->second.SpellDataVec.end(); itr++)
        {
            if (*itr > 0)
                continue;

            auto spellInfo = sSpellMgr.GetSpellEntry(abs(*itr));
            if (spellInfo)
                oss << "|cFFFFCC00『增益』『" << spellInfo->SpellName[4] << "』|r\n";
        }

        for (auto itr = iter->second.CommandDataVec.begin(); itr != iter->second.CommandDataVec.end(); itr++)
            if (!itr->command.empty())
                oss << "|cFFFFCC00『其他』" << itr->des << "\n";

        return oss.str();
    }

    return "";
}


std::string Reward::GetAnounceText(uint32 rewId)
{
    std::unordered_map<uint32, RewTemplate>::iterator iter = RewMap.find(rewId);
    if (iter != RewMap.end())
    {
        std::ostringstream oss;

        // if (iter->second.goldCount > 0)
        //	oss << "|cFFFFCC00[金币]|rx|cFFFFCC00" << iter->second.goldCount << " ";
        // if (iter->second.tokenCount > 0)
        //	oss << "|cFFFFCC00[" << sString->GetText(CORE_STR_TYPES(STR_TOKEN)) << "]|rx|cFFFFCC00" << iter->second.tokenCount << " ";
        // if (iter->second.xp > 0)
        //	oss << "|cFFFFCC00[经验]|rx|cFFFFCC00" << iter->second.xp << " ";
        // if (iter->second.hrPoints > 0)
        //	oss << "|cFFFFCC00[荣誉]|rx|cFFFFCC00" << iter->second.hrPoints << " ";

        // if (iter->second.arenaPoints > 0)
        //	oss << "|cFFFFCC00[竞技点]|rx|cFFFFCC00" << iter->second.arenaPoints << " ";

        // if (iter->second.statPoints > 0)
        //	oss << "|cFFFFCC00[斗气点]|rx|cFFFFCC00" << iter->second.statPoints << " ";

        // for (auto itr = iter->second.ItemDataVec.begin(); itr != iter->second.ItemDataVec.end(); itr++)
        //	oss << "" << sCF->GetItemLink(itr->itemId) << "x|cFFFFCC00" << itr->itemCount << " ";
        //


        // for (auto itr = iter->second.SpellDataVec.begin(); itr != iter->second.SpellDataVec.end(); itr++)
        //{
        //	int32 id = *itr;
        //	if (id > 0)
        //		continue;

        //	oss << "获得增益|cFFFFCC00[" << sSpellMgr->GetSpellInfo(abs(id))->SpellName[4] << "] ";
        //}

        // oss << "|r";

        return oss.str();
    }


    return "";
}

void Reward::LoadDamCreToSend()
{
    VCreatureDamageSend.clear();
    auto itemup = WorldDatabase.PQuery("select 生物编号,最低伤害量,排行奖励,排行弹窗 from _生物_伤害奖励");
    if (itemup)
    {
        do
        {
            CreatureDamageSend tmpItem;
            tmpItem.creatureid = itemup->Fetch()[0].GetUInt32();
            tmpItem.mindamage = itemup->Fetch()[1].GetUInt32();
            Tokenizer senditems(itemup->Fetch()[2].GetString(), '#');
            tmpItem.sendgossipcount = itemup->Fetch()[3].GetUInt32();

            tmpItem.maxsend = senditems.size();
            if (tmpItem.maxsend)
            {
                for (uint32 i = 0; i < tmpItem.maxsend; i++)
                {
                    tmpItem.itemsends[i] = atoi(senditems[i] ? senditems[i] : 0);
                }
            }
            VCreatureDamageSend.insert(CreatureDamageSend_t::value_type(tmpItem.creatureid, tmpItem));
        }
        while (itemup->NextRow());
    }

    delete itemup;
    sLog.outString(">>加载 _生物_伤害奖励 成功.");
}



void Reward::SetJiFenByPlayer(Player* player, uint32 desTokenCount)
{
    // player->pTokens = (player->pTokens - desTokenCount) < 0 ? 0 : (player->pTokens - desTokenCount);
    player->pTokens = (player->pTokens > desTokenCount) ? (player->pTokens - desTokenCount) : 0;
    //替换字符点券
    player->SendChatMessage("|cff1eff00※提示※:|r 您刚消费了点券 |cFFFF0000%u|r点，当前余额：|cFF00FF00%u|r点", desTokenCount, player->pTokens);
    CharacterDatabase.PExecute("UPDATE `_玩家信息额外扩展` SET 已消费积分=已消费积分+%u  WHERE guid = %u", desTokenCount, player->GetGUIDLow());
    SavePlayerExtraInfo(player);
}

void Reward::AddJiFenByPlayer(Player* player, uint32 desTokenCount)
{
    player->pTokens += desTokenCount;
    std::ostringstream oss;
    oss << "获得了|cFFFFCC00[" << "点券" << "]|r ×" << desTokenCount;
    player->GetSession()->SendAreaTriggerMessage(oss.str().c_str());
    player->SendChatMessage("|cff1eff00※提示※:|r 恭喜您%s", oss.str().c_str());
    SavePlayerExtraInfo(player);

    // 记录玩家获得点券记录
    CharacterDatabase.PExecute(
        "INSERT INTO `_玩家点券领取记录` (`guid`, `玩家名`, `点券`, `金币`, `硬核模式`, `时间`, `accountid`) VALUES (%u, '%s', %u, 0, %u, NOW(), %u)",
        player->GetGUIDLow(),
        player->GetName(),
        desTokenCount,
        player->IsHardcore() ? 1 : 0,
        player->GetSession()->GetAccountId()
    );
}


void Reward::SavePlayerExtraInfo(Player* player)
{
    // sVip->VipMap.at(player->pVipLevel).VipLootRate
    CharacterDatabase.PExecute(
        "UPDATE `_玩家信息额外扩展` SET "
        "会员等级=%u, "
        "积分=%u, "
        "天赋点=%u, "
        "额外爆率=%u, "
        "转生=%u, "
        "斗气等级=%u, "
        "斗气值=%u, "
        "捐献=%u, "
        "宝宝升级等级=%u, "
        "额外金币=%u, " // Added comma here
        "鞭尸几率=%u "  // Removed comma here
        "WHERE guid = %u",
        player->pVipLevel,
        player->pTokens,
        player->pExtraTalentPT,
        player->pExtraLootRatePT,
        player->pZhuanShenLevel,
        player->pDouqiLevel,
        player->pDouqiPT,
        player->pJuanxianPT,
        player->PetUpgradeLevel,
        player->m_Loot_Gold_Rate,
        player->m_BossRevive_Rate,
        player->GetGUIDLow()
    );
}
